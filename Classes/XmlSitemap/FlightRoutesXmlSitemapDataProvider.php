<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\XmlSitemap;

use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Seo\XmlSitemap\AbstractXmlSitemapDataProvider;

/**
 * Flight Routes XML Sitemap Data Provider
 *
 * Generates sitemap entries for active flight routes.
 * URLs are formed from flight route slugs.
 * Change frequency and priority are taken from the template page.
 */
class FlightRoutesXmlSitemapDataProvider extends AbstractXmlSitemapDataProvider
{
    private FlightRouteRepository $flightRouteRepository;

    public function __construct(
        ServerRequestInterface $request,
        string $name,
        array $config = [],
        ?ContentObjectRenderer $cObj = null
    ) {
        parent::__construct($request, $name, $config, $cObj);
        $this->flightRouteRepository = GeneralUtility::makeInstance(FlightRouteRepository::class);
    }

    public function getItems(): array
    {
        $items = [];
        $site = $this->getCurrentSite();
        
        if (!$site) {
            return $items;
        }
var_dump(4); exit;
        // Get all active flight routes for this site
        $routes = $this->flightRouteRepository->findActiveBySite($site->getIdentifier());

        foreach ($routes as $route) {
            // Get the landing page for this route
            $landingPage = $this->getLandingPageForRoute($route);
            
            if (!$landingPage || !$this->shouldIncludeInSitemap($landingPage)) {
                continue;
            }

            // Get template page for SEO settings
            $templatePage = $this->getTemplatePageForLandingPage($landingPage);
            
            $items[] = [
                'loc' => $this->buildVirtualRouteUrl($landingPage, $route, $site),
                'lastMod' => $route->getTstamp()->getTimestamp(),
                'changefreq' => $this->getChangeFrequency($templatePage),
                'priority' => $this->getPriority($templatePage)
            ];
        }

        return $items;
    }

    public function getLastModified(): int
    {
        $site = $this->getCurrentSite();
        if (!$site) {
            return 0;
        }

        $routes = $this->flightRouteRepository->findActiveBySite($site->getIdentifier());
        $lastModified = 0;

        foreach ($routes as $route) {
            $timestamp = $route->getTstamp()->getTimestamp();
            if ($timestamp > $lastModified) {
                $lastModified = $timestamp;
            }
        }

        return $lastModified;
    }

    public function getNumberOfPages(): int
    {
        $site = $this->getCurrentSite();
        if (!$site) {
            return 0;
        }

        // Get all active flight routes for this site
        $routes = $this->flightRouteRepository->findActiveBySite($site->getIdentifier());
        $count = 0;

        foreach ($routes as $route) {
            // Get the landing page for this route
            $landingPage = $this->getLandingPageForRoute($route);

            if ($landingPage && $this->shouldIncludeInSitemap($landingPage)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get the landing page for a flight route
     */
    private function getLandingPageForRoute($route): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($route->getPid(), \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative() ?: null;
    }

    /**
     * Check if landing page should be included in sitemap
     */
    private function shouldIncludeInSitemap(array $landingPage): bool
    {
        return (bool)($landingPage['tx_flightlandingpages_enable_sitemap'] ?? false);
    }
    public function getNumberOfPages()
    {
        return 1;
    }

    /**
     * Get template page for landing page
     */
    private function getTemplatePageForLandingPage(array $landingPage): ?array
    {
        $templatePageUid = (int)($landingPage['tx_flightlandingpages_template_page'] ?? 0);
        
        if ($templatePageUid === 0) {
            return null;
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(200, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative() ?: null;
    }

    /**
     * Build virtual route URL
     */
    private function buildVirtualRouteUrl(array $landingPage, $route, Site $site): string
    {
        $baseUrl = (string)$site->getBase();
        $landingPageSlug = $landingPage['slug'] ?? '';
        $routeSlug = $route->getRouteSlug();
        
        return rtrim($baseUrl, '/') . $landingPageSlug . '/' . $routeSlug;
    }

    /**
     * Get change frequency from template page
     */
    private function getChangeFrequency(?array $templatePage): string
    {
        if (!$templatePage) {
            return 'weekly';
        }
        
        return $templatePage['sitemap_changefreq'] ?? 'weekly';
    }

    /**
     * Get priority from template page
     */
    private function getPriority(?array $templatePage): string
    {
        if (!$templatePage) {
            return '0.5';
        }
        
        $priority = $templatePage['sitemap_priority'] ?? '0.5';
        return (string)$priority;
    }

    /**
     * Get current site from request
     */
    private function getCurrentSite(): ?Site
    {
        return $this->request->getAttribute('site');
    }
}
