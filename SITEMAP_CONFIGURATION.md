# Flight Routes XML Sitemap Configuration

This document explains how the Flight Landing Pages extension adds flight routes to XML sitemaps.

## Overview

The extension provides a custom XML sitemap data provider that automatically includes active flight routes in your site's XML sitemap. This ensures that search engines can discover and index your virtual flight route URLs.

## How It Works

### 1. Automatic Sitemap Generation

The `FlightRoutesXmlSitemapDataProvider` automatically:
- Finds all active flight routes for the current site
- Checks if the landing page has sitemap enabled
- Generates URLs using the landing page slug + route slug pattern
- Uses template page SEO settings for change frequency and priority

### 2. URL Structure

Virtual route URLs are generated as:
```
{site_base_url}{landing_page_slug}/{route_slug}
```

Example:
- Site: `https://example.com`
- Landing page slug: `/flights`
- Route slug: `ber-sof`
- Final URL: `https://example.com/flights/ber-sof`

### 3. SEO Settings Source

The sitemap provider gets SEO settings from the template page:
- **Change Frequency**: From template page `sitemap_changefreq` field (default: 'weekly')
- **Priority**: From template page `sitemap_priority` field (default: '0.5')
- **Last Modified**: From flight route `tstamp` field

## Configuration

### 1. Enable Sitemap for Landing Pages

In the backend, edit your Flight Landing Page (doktype 201):
1. Go to the "Landing Page" tab
2. Enable "Enable in Sitemap" checkbox
3. Save the page

### 2. Configure Template Page SEO Settings

Edit your Flight Template Page (doktype 200):
1. Go to the "SEO" tab
2. Set "Change frequency" (e.g., weekly, monthly)
3. Set "Priority" (0.0 to 1.0, default 0.5)
4. Save the page

### 3. TypoScript Configuration

The sitemap provider is automatically configured via TypoScript:

```typoscript
plugin.tx_seo {
    config {
        xmlSitemap {
            sitemaps {
                flightRoutes {
                    provider = Bgs\FlightLandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider
                    config {
                        # Configuration can be added here if needed in the future
                    }
                }
            }
        }
    }
}
```

## Accessing the Sitemap

### 1. Sitemap Index

Access your sitemap index at:
```
https://yoursite.com/?type=**********
```

### 2. Flight Routes Sitemap

The flight routes will appear in a separate sitemap section within the sitemap index.

### 3. With Routing Configuration

If you have routing configured for sitemaps:
```
https://yoursite.com/sitemap.xml
```

## Features

### 1. Site-Aware

- Only includes routes for the current site
- Respects multi-site configurations
- Uses correct base URLs for each site

### 2. Performance Optimized

- Only queries active flight routes
- Caches results according to TYPO3 sitemap caching
- Minimal database queries

### 3. SEO Friendly

- Proper last modified timestamps
- Configurable change frequency
- Configurable priority values
- Clean URL structure

### 4. Conservative Implementation

- Doesn't modify existing functionality
- Uses existing TYPO3 sitemap infrastructure
- Respects existing SEO settings
- No changes to core behavior

## Troubleshooting

### 1. Routes Not Appearing in Sitemap

Check:
- Landing page has "Enable in Sitemap" enabled
- Flight routes are active (`is_active = 1`)
- Landing page is not hidden
- Template page exists and is not deleted

### 2. Wrong SEO Settings

Check:
- Template page SEO tab settings
- Template page is correctly linked to landing page
- Template page is not deleted

### 3. Wrong URLs

Check:
- Landing page slug is correct
- Route slugs are generated correctly
- Site base URL is configured properly

## Customization

The sitemap provider can be extended or customized by:
1. Creating a custom provider extending `FlightRoutesXmlSitemapDataProvider`
2. Overriding specific methods (e.g., `getChangeFrequency`, `getPriority`)
3. Adding custom TypoScript configuration

Example custom provider:
```php
class CustomFlightRoutesXmlSitemapDataProvider extends FlightRoutesXmlSitemapDataProvider
{
    protected function getChangeFrequency(?array $templatePage): string
    {
        // Custom logic for change frequency
        return 'daily';
    }
}
```

## Integration with EXT:seo

This provider integrates seamlessly with TYPO3's built-in SEO extension:
- Uses standard `AbstractXmlSitemapDataProvider` base class
- Follows TYPO3 sitemap conventions
- Respects sitemap caching mechanisms
- Works with sitemap routing configurations
