# Flight Landing Pages Extension Constants

# Import fluid_styled_content constants
@import 'EXT:fluid_styled_content/Configuration/TypoScript/constants.typoscript'

plugin.tx_flightlandingpages {
    view {
        # cat=plugin.tx_flightlandingpages/file; type=string; label=Path to template root (FE)
        templateRootPath = EXT:flight_landing_pages/Resources/Private/Templates/
        # cat=plugin.tx_flightlandingpages/file; type=string; label=Path to template partials (FE)
        partialRootPath = EXT:flight_landing_pages/Resources/Private/Partials/
        # cat=plugin.tx_flightlandingpages/file; type=string; label=Path to template layouts (FE)
        layoutRootPath = EXT:flight_landing_pages/Resources/Private/Layouts/
    }
    persistence {
        # cat=plugin.tx_flightlandingpages//a; type=string; label=Default storage PID
        storagePid =
    }
    settings {
        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }
    }
}

# XML Sitemap configuration for Flight Routes
plugin.tx_seo {
    config {
        xmlSitemap {
            sitemaps {
                flightRoutes {
                    provider = Bgs\FlightLandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider
                    config {
                        # Configuration can be added here if needed in the future
                    }
                }
            }
        }
    }
}
